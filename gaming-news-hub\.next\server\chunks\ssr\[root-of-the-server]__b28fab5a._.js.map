{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/s/%5BshortCode%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Script from 'next/script';\nimport Head from 'next/head';\n\ninterface LinkData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: string;\n}\n\nexport default function RedirectPage() {\n  const params = useParams();\n  const router = useRouter();\n  const shortCode = params.shortCode as string;\n  \n  const [linkData, setLinkData] = useState<LinkData | null>(null);\n  const [countdown, setCountdown] = useState(30); // 30 seconds countdown\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [waitingPhase, setWaitingPhase] = useState(true); // 20 second wait phase\n  const [adsVisible, setAdsVisible] = useState(false); // Show ads and continue button\n  const [countdownStarted, setCountdownStarted] = useState(false); // 30 second countdown phase\n\n  // Simple check - if URL has quiz=completed, show final page, otherwise redirect\n  const [isFromQuiz, setIsFromQuiz] = useState(false);\n  const [shouldRedirect, setShouldRedirect] = useState(false);\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const hasQuizParam = window.location.search.includes('quiz=completed');\n    console.log('Final page: Has quiz param:', hasQuizParam);\n\n    if (hasQuizParam) {\n      setIsFromQuiz(true);\n      // Clean URL\n      window.history.replaceState({}, '', window.location.pathname);\n    } else {\n      setShouldRedirect(true);\n    }\n  }, []);\n\n  // Redirect if needed\n  useEffect(() => {\n    if (shouldRedirect) {\n      router.replace(`/s/${shortCode}/content-gateway-7h3p9`);\n    }\n  }, [shouldRedirect, router, shortCode]);\n\n  // Fetch link data\n  useEffect(() => {\n    const fetchLinkData = async () => {\n      try {\n        console.log('Fetching link data for shortCode:', shortCode);\n        const response = await fetch(`/api/redirect/${shortCode}`);\n        const data = await response.json();\n\n        console.log('API response:', response.status, data);\n\n        if (response.ok) {\n          setLinkData(data);\n          console.log('Link data set successfully:', data);\n        } else {\n          console.log('API error:', data.error);\n          setError(data.error || 'Link not found');\n        }\n      } catch (err) {\n        console.log('Fetch error:', err);\n        setError('Failed to load link data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchLinkData();\n  }, [shortCode]);\n\n  // Phase 1: 20-second waiting period (no timer shown)\n  useEffect(() => {\n    if (!loading && !error && waitingPhase) {\n      console.log('Starting 20-second waiting phase...');\n      const timer = setTimeout(() => {\n        setWaitingPhase(false);\n        setAdsVisible(true);\n        console.log('20 seconds completed - showing ads and continue button');\n      }, 20000); // 20 seconds\n\n      return () => clearTimeout(timer);\n    }\n  }, [loading, error, waitingPhase]);\n\n  // Phase 2: 30-second countdown timer (visible)\n  useEffect(() => {\n    if (!countdownStarted || countdown <= 0) return;\n\n    const timer = setInterval(() => {\n      setCountdown((prev) => {\n        if (prev <= 1) {\n          clearInterval(timer);\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [countdownStarted, countdown]);\n\n  const handleContinueClick = () => {\n    console.log('Continue button clicked! Starting 30-second countdown...');\n    setCountdownStarted(true);\n    setCountdown(30);\n  };\n\n  const handleRedirect = async () => {\n    if (linkData?.originalUrl) {\n      try {\n        // Increment click count\n        await fetch(`/api/redirect/${shortCode}`, { method: 'POST' });\n      } catch (err) {\n        console.log('Failed to increment clicks, but continuing...');\n      }\n\n      // Redirect to original URL\n      window.location.href = linkData.originalUrl;\n    } else {\n      // Fallback if no link data\n      window.location.href = 'https://google.com';\n    }\n  };\n\n  // If should redirect, don't show anything\n  if (shouldRedirect) {\n    return null;\n  }\n\n  // If not from quiz and not loading, don't show anything (will redirect)\n  if (!isFromQuiz && !loading) {\n    return null;\n  }\n\n  // Show loading only if from quiz\n  if (loading && isFromQuiz) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-white text-xl\">Welcome! Please wait...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Don't show error screen - replace with welcome message\n  if (error) {\n    setError(null); // Clear error and continue with default behavior\n    setLinkData({\n      originalUrl: 'https://google.com',\n      clicks: 0,\n      createdAt: new Date().toISOString()\n    });\n  }\n\n  // Only show final page if coming from quiz\n  if (!isFromQuiz) {\n    return null;\n  }\n\n  return (\n    <>\n      <Head>\n        <title>Get Your Link - GameHub</title>\n      </Head>\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Header */}\n      <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n        <div className=\"max-w-4xl mx-auto flex justify-between items-center\">\n          <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n            🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n          </Link>\n          <div className=\"text-purple-400 font-mono\">\n            Final Step - Step 3 of 3 | {waitingPhase ? 'Preparing...' :\n             adsVisible && !countdownStarted ? 'Ready' :\n             countdownStarted ? `${countdown}s remaining` : 'Ready'}\n          </div>\n        </div>\n      </nav>\n\n      <div className=\"flex items-center justify-center min-h-[calc(100vh-80px)] p-4\">\n        <div className=\"max-w-2xl w-full\">\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n\n            {/* Phase 1: 20-second waiting (no timer shown) */}\n            {waitingPhase && (\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold text-white mb-6\">\n                  Welcome! Please wait\n                </h2>\n                <div className=\"flex items-center justify-center space-x-2 mb-6\">\n                  <div className=\"w-3 h-3 bg-purple-400 rounded-full animate-bounce\"></div>\n                  <div className=\"w-3 h-3 bg-pink-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                  <div className=\"w-3 h-3 bg-purple-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                </div>\n                <p className=\"text-gray-300\">\n                  Setting up your secure link...\n                </p>\n              </div>\n            )}\n\n            {/* Phase 2: Ads visible + Continue button */}\n            {adsVisible && !countdownStarted && (\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold text-white mb-6\">\n                  Almost Ready!\n                </h2>\n                <p className=\"text-gray-300 mb-6\">\n                  Please wait for the advertisements to load, then click Continue\n                </p>\n\n                {/* Montag ads will appear here automatically */}\n                <div className=\"bg-white/5 rounded-lg p-6 mb-6 min-h-[200px] flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-4\">📺</div>\n                    <p className=\"text-gray-400 text-sm\">\n                      Advertisements loading...\n                    </p>\n                  </div>\n                </div>\n\n                <button\n                  onClick={handleContinueClick}\n                  className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105\"\n                >\n                  Continue →\n                </button>\n              </div>\n            )}\n\n            {/* Phase 3: 30-second countdown (visible timer) */}\n            {countdownStarted && countdown > 0 && (\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold text-white mb-6\">\n                  Please Wait\n                </h2>\n                <div className=\"bg-purple-600 text-white rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 text-3xl font-bold\">\n                  {countdown}\n                </div>\n                <p className=\"text-purple-300 mb-4\">\n                  ⏳ Please wait {countdown} seconds...\n                </p>\n                <div className=\"bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold\">\n                  Countdown Active\n                </div>\n              </div>\n            )}\n\n            {/* Countdown Display */}\n            <div className=\"text-center mb-6\">\n              <div className=\"bg-white/5 rounded-lg p-6\">\n                {countdownStarted ? (\n                  <div className=\"text-center\">\n                    <div className=\"bg-purple-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold\">\n                      {countdown}\n                    </div>\n                    <p className=\"text-purple-300 mb-4\">\n                      ⏳ Please wait {countdown} seconds...\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"text-center\">\n                    <p className=\"text-yellow-300 mb-4\">\n                      ⏳ Click an advertisement above to start the countdown\n                    </p>\n                  </div>\n                )}\n\n                <div className=\"bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold\">\n                  {countdownStarted ? 'Countdown Active' : 'Waiting for Ad Interaction'}\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* Final redirect section - only show after countdown */}\n            {countdown <= 0 && countdownStarted && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mt-6\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 text-center\">Link Ready!</h2>\n                <p className=\"text-gray-300 mb-6 text-center\">Click the button below to get your link:</p>\n\n                <button\n                  onClick={handleRedirect}\n                  className=\"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full\"\n                >\n                  Get Your Link\n                </button>\n\n                <Link href=\"/\" className=\"block mt-4 text-purple-400 hover:text-purple-300 transition-colors text-center\">\n                  ← Back to GameHub\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Montag Ad Network Script */}\n      <Script\n        src=\"https://fpyf8.com/88/tag.min.js\"\n        data-zone=\"156349\"\n        async\n        data-cfasync=\"false\"\n        strategy=\"afterInteractive\"\n      />\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uBAAuB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,uBAAuB;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,+BAA+B;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,4BAA4B;IAE7F,gFAAgF;IAChF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;IAUR,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,OAAO,OAAO,CAAC,CAAC,GAAG,EAAE,UAAU,sBAAsB,CAAC;QACxD;IACF,GAAG;QAAC;QAAgB;QAAQ;KAAU;IAEtC,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC,qCAAqC;gBACjD,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,QAAQ,GAAG,CAAC,iBAAiB,SAAS,MAAM,EAAE;gBAE9C,IAAI,SAAS,EAAE,EAAE;oBACf,YAAY;oBACZ,QAAQ,GAAG,CAAC,+BAA+B;gBAC7C,OAAO;oBACL,QAAQ,GAAG,CAAC,cAAc,KAAK,KAAK;oBACpC,SAAS,KAAK,KAAK,IAAI;gBACzB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc;YACtC,QAAQ,GAAG,CAAC;YACZ,MAAM,QAAQ,WAAW;gBACvB,gBAAgB;gBAChB,cAAc;gBACd,QAAQ,GAAG,CAAC;YACd,GAAG,QAAQ,aAAa;YAExB,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;QAAO;KAAa;IAEjC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,oBAAoB,aAAa,GAAG;QAEzC,MAAM,QAAQ,YAAY;YACxB,aAAa,CAAC;gBACZ,IAAI,QAAQ,GAAG;oBACb,cAAc;oBACd,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAkB;KAAU;IAEhC,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC;QACZ,oBAAoB;QACpB,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,aAAa;YACzB,IAAI;gBACF,wBAAwB;gBACxB,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;oBAAE,QAAQ;gBAAO;YAC7D,EAAE,OAAO,KAAK;gBACZ,QAAQ,GAAG,CAAC;YACd;YAEA,2BAA2B;YAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,WAAW;QAC7C,OAAO;YACL,2BAA2B;YAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,0CAA0C;IAC1C,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,wEAAwE;IACxE,IAAI,CAAC,cAAc,CAAC,SAAS;QAC3B,OAAO;IACT;IAEA,iCAAiC;IACjC,IAAI,WAAW,YAAY;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,yDAAyD;IACzD,IAAI,OAAO;QACT,SAAS,OAAO,iDAAiD;QACjE,YAAY;YACV,aAAa;YACb,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,2CAA2C;IAC3C,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBACE;;0BACE,8OAAC,oKAAA,CAAA,UAAI;0BACH,cAAA,8OAAC;8BAAM;;;;;;;;;;;0BAET,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;wCAAmF;sDACvG,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAElG,8OAAC;oCAAI,WAAU;;wCAA4B;wCACb,eAAe,iBAC1C,cAAc,CAAC,mBAAmB,UAClC,mBAAmB,GAAG,UAAU,WAAW,CAAC,GAAG;;;;;;;;;;;;;;;;;;kCAKtD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCAGZ,8BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAC,gBAAgB;wDAAM;;;;;;kEAC/F,8OAAC;wDAAI,WAAU;wDAAoD,OAAO;4DAAC,gBAAgB;wDAAM;;;;;;;;;;;;0DAEnG,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;oCAOhC,cAAc,CAAC,kCACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DAGnD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAKlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAMzC,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;oCAOJ,oBAAoB,YAAY,mBAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;0DACZ;;;;;;0DAEH,8OAAC;gDAAE,WAAU;;oDAAuB;oDACnB;oDAAU;;;;;;;0DAE3B,8OAAC;gDAAI,WAAU;0DAA+D;;;;;;;;;;;;kDAOlF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,iCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ;;;;;;sEAEH,8OAAC;4DAAE,WAAU;;gEAAuB;gEACnB;gEAAU;;;;;;;;;;;;yEAI7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAuB;;;;;;;;;;;8DAMxC,8OAAC;oDAAI,WAAU;8DACZ,mBAAmB,qBAAqB;;;;;;;;;;;;;;;;;oCAQ9C,aAAa,KAAK,kCACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAAiC;;;;;;0DAE9C,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAID,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAiF;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYpH,8OAAC,8HAAA,CAAA,UAAM;wBACL,KAAI;wBACJ,aAAU;wBACV,KAAK;wBACL,gBAAa;wBACb,UAAS;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}