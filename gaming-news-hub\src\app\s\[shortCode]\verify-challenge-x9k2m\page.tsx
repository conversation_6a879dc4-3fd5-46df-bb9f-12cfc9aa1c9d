'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';
import Head from 'next/head';

interface Question {
  question: string;
  options: number[];
  correct: number;
}

export default function QuizPage() {
  const params = useParams();
  const router = useRouter();
  const shortCode = params.shortCode as string;
  
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const [wrongAnswer, setWrongAnswer] = useState(false);
  const [countdown, setCountdown] = useState(30);
  const [showContinue, setShowContinue] = useState(false);

  // Generate random math questions
  const generateQuestions = (): Question[] => {
    const questions: Question[] = [];
    
    for (let i = 0; i < 3; i++) {
      const type = Math.floor(Math.random() * 3); // 0: addition, 1: subtraction, 2: multiplication
      let question: Question;
      
      if (type === 0) {
        // Addition
        const a = Math.floor(Math.random() * 50) + 1;
        const b = Math.floor(Math.random() * 50) + 1;
        const correct = a + b;
        const options = [correct];
        while (options.length < 4) {
          const wrong = correct + Math.floor(Math.random() * 20) - 10;
          if (wrong > 0 && !options.includes(wrong)) {
            options.push(wrong);
          }
        }
        question = {
          question: `${a} + ${b} = ?`,
          options: options.sort(() => Math.random() - 0.5),
          correct
        };
      } else if (type === 1) {
        // Subtraction
        const a = Math.floor(Math.random() * 50) + 20;
        const b = Math.floor(Math.random() * (a - 1)) + 1;
        const correct = a - b;
        const options = [correct];
        while (options.length < 4) {
          const wrong = correct + Math.floor(Math.random() * 20) - 10;
          if (wrong >= 0 && !options.includes(wrong)) {
            options.push(wrong);
          }
        }
        question = {
          question: `${a} - ${b} = ?`,
          options: options.sort(() => Math.random() - 0.5),
          correct
        };
      } else {
        // Multiplication
        const a = Math.floor(Math.random() * 12) + 1;
        const b = Math.floor(Math.random() * 12) + 1;
        const correct = a * b;
        const options = [correct];
        while (options.length < 4) {
          const wrong = correct + Math.floor(Math.random() * 20) - 10;
          if (wrong > 0 && !options.includes(wrong)) {
            options.push(wrong);
          }
        }
        question = {
          question: `${a} × ${b} = ?`,
          options: options.sort(() => Math.random() - 0.5),
          correct
        };
      }
      
      questions.push(question);
    }
    
    return questions;
  };

  const [questions] = useState<Question[]>(generateQuestions());

  // Countdown for wrong answers
  useEffect(() => {
    if (wrongAnswer && countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setShowContinue(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [wrongAnswer, countdown]);

  const handleAnswer = (selectedAnswer: number) => {
    if (selectedAnswer === questions[currentQuestion].correct) {
      setScore(score + 1);
      if (currentQuestion + 1 < questions.length) {
        setCurrentQuestion(currentQuestion + 1);
      } else {
        setShowResult(true);
        setShowContinue(true);
      }
    } else {
      setWrongAnswer(true);
    }
  };

  const handleContinue = () => {
    console.log('Quiz page: Setting flag and navigating to final page');
    // Set flag to indicate we're coming from quiz page
    sessionStorage.setItem(`fromQuizPage_${shortCode}`, 'true');

    // Small delay to ensure sessionStorage is set
    setTimeout(() => {
      router.push(`/s/${shortCode}`);
    }, 100);
  };

  return (
    <>
      <Head>
        <title>Math Quiz - GameHub Link</title>
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Header */}
        <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
          <div className="max-w-4xl mx-auto flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer">
              🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
            </Link>
            <div className="text-purple-400 font-mono">
              Math Quiz - Step 2 of 3
            </div>
          </div>
        </nav>

        <div className="max-w-4xl mx-auto p-6">


          {/* Quiz Content */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
            <h1 className="text-3xl font-bold text-white mb-6 text-center">
              🧮 Quick Math Challenge
            </h1>

            {!wrongAnswer && !showResult && (
              <div className="text-center">
                <div className="mb-6">
                  <span className="text-purple-300 text-lg">
                    Question {currentQuestion + 1} of {questions.length}
                  </span>
                </div>

                <div className="bg-purple-900/30 rounded-lg p-8 mb-8">
                  <h2 className="text-4xl font-bold text-white mb-8">
                    {questions[currentQuestion].question}
                  </h2>

                  <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                    {questions[currentQuestion].options.map((option, index) => (
                      <button
                        key={index}
                        onClick={() => handleAnswer(option)}
                        className="bg-white/10 hover:bg-white/20 text-white text-xl font-semibold py-4 px-6 rounded-lg transition-all transform hover:scale-105"
                      >
                        {option}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {wrongAnswer && !showContinue && (
              <div className="text-center">
                <div className="bg-red-900/30 rounded-lg p-8 mb-8">
                  <h2 className="text-3xl font-bold text-red-400 mb-4">❌ Wrong Answer!</h2>
                  <p className="text-gray-300 mb-6">
                    You gave the wrong answer. Please wait for the countdown to finish.
                  </p>
                  <div className="bg-red-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                    {countdown}
                  </div>
                  <p className="text-red-300">
                    Wait {countdown} seconds...
                  </p>
                </div>
              </div>
            )}

            {showResult && (
              <div className="text-center">
                <div className="bg-green-900/30 rounded-lg p-8 mb-8">
                  <h2 className="text-3xl font-bold text-green-400 mb-4">🎉 Congratulations!</h2>
                  <p className="text-gray-300 mb-6">
                    You answered all questions correctly! You can now proceed to get your link.
                  </p>
                  <div className="text-2xl font-bold text-green-300">
                    Score: {score}/{questions.length}
                  </div>
                </div>
              </div>
            )}
          </div>



          {/* Blog Content */}
          <article className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
            <h2 className="text-2xl font-bold text-white mb-4">
              🧠 Why Math Skills Matter in Gaming
            </h2>
            <p className="text-gray-300 mb-4 leading-relaxed">
              Mathematics plays a crucial role in gaming, from calculating damage per second (DPS) in RPGs to 
              understanding probability in strategy games. Many successful gamers have strong analytical skills 
              that help them optimize their gameplay and make strategic decisions.
            </p>
            <p className="text-gray-300 mb-4 leading-relaxed">
              Whether you're managing resources in a city-building game or calculating the best build order 
              in a real-time strategy game, mathematical thinking gives you a competitive edge.
            </p>
          </article>



          {/* Continue Button */}
          {showContinue && (
            <div className="text-center mb-8">
              <button
                onClick={handleContinue}
                className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 text-lg"
              >
                Continue to Final Step →
              </button>
            </div>
          )}
        </div>

        {/* Montag Multitag Ad Network Script */}
        <Script
          src="https://fpyf8.com/88/tag.min.js"
          data-zone="156349"
          async
          data-cfasync="false"
          strategy="afterInteractive"
          onLoad={() => console.log('Page 2: Montag multitag script loaded')}
          onError={() => console.log('Page 2: Montag multitag script failed')}
        />
      </div>
    </>
  );
}
