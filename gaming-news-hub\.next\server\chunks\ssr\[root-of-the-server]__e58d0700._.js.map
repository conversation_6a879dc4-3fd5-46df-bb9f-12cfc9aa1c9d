{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/s/%5BshortCode%5D/content-gateway-7h3p9/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Script from 'next/script';\nimport Head from 'next/head';\n\ninterface LinkData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: string;\n}\n\nexport default function BlogPage() {\n  const params = useParams();\n  const router = useRouter();\n  const shortCode = params.shortCode as string;\n  \n  const [linkData, setLinkData] = useState<LinkData | null>(null);\n  const [loading, setLoading] = useState(false); // Start with false to prevent flash\n  const [error, setError] = useState<string | null>(null);\n  const [initialCountdown, setInitialCountdown] = useState(10); // 10 second initial countdown\n  const [showFirstContinue, setShowFirstContinue] = useState(false); // Show first continue button\n  const [secondCountdown, setSecondCountdown] = useState(20); // 20 second second countdown\n  const [showFinalContinue, setShowFinalContinue] = useState(false); // Show final continue button\n  const [secondPhase, setSecondPhase] = useState(false); // Track if in second phase\n\n  // Fetch link data in background - don't block the page\n  useEffect(() => {\n    // Set default data immediately\n    setLinkData({\n      originalUrl: 'https://example.com',\n      clicks: 0,\n      createdAt: new Date().toISOString()\n    });\n\n    // Try to fetch real data in background\n    const fetchLinkData = async () => {\n      try {\n        const response = await fetch(`/api/redirect/${shortCode}`);\n        const data = await response.json();\n\n        if (response.ok) {\n          setLinkData(data);\n        }\n      } catch (err) {\n        console.log('Failed to fetch link data, using default');\n      }\n    };\n\n    fetchLinkData();\n  }, [shortCode]);\n\n  // Phase 1: 10-second initial countdown\n  useEffect(() => {\n    if (!loading && !error && initialCountdown > 0 && !secondPhase) {\n      const timer = setInterval(() => {\n        setInitialCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(timer);\n            setShowFirstContinue(true);\n            console.log('Blog page: 10 seconds completed, showing first continue button');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(timer);\n    }\n  }, [loading, error, initialCountdown, secondPhase]);\n\n  // Phase 2: 20-second countdown after first continue clicked\n  useEffect(() => {\n    if (secondPhase && secondCountdown > 0) {\n      const timer = setInterval(() => {\n        setSecondCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(timer);\n            setShowFinalContinue(true);\n            console.log('Blog page: 20 seconds completed, showing final continue button');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(timer);\n    }\n  }, [secondPhase, secondCountdown]);\n\n  // Monitor for Montag ads loading\n  useEffect(() => {\n    if (!loading && !error) {\n      const checkAds = setInterval(() => {\n        // Check for Montag ad elements\n        const adElements = document.querySelectorAll('[class*=\"ad\"], [id*=\"ad\"], iframe');\n        const montagScripts = document.querySelectorAll('script[src*=\"fpyf8\"]');\n        const socialAds = document.querySelectorAll('[class*=\"social\"], [class*=\"montag\"]');\n\n        console.log('Page 1 - Montag ads monitoring:', {\n          adElements: adElements.length,\n          montagScripts: montagScripts.length,\n          socialAds: socialAds.length,\n          bodyChildren: document.body.children.length\n        });\n      }, 8000); // Check every 8 seconds\n\n      return () => clearInterval(checkAds);\n    }\n  }, [loading, error]);\n\n  const handleFirstContinue = () => {\n    console.log('Blog page: Starting second phase (20-second countdown)');\n    setSecondPhase(true);\n    setShowFirstContinue(false);\n  };\n\n  const handleFinalContinue = () => {\n    console.log('Blog page: Navigating to quiz page');\n    router.push(`/s/${shortCode}/verify-challenge-x9k2m`);\n  };\n\n  // Show minimal loading, no error screen to prevent flash\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-white text-xl\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Don't show error screen - just continue with default data\n\n  return (\n    <>\n      <Head>\n        <title>Gaming Blog - GameHub Link</title>\n      </Head>\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n        {/* Header */}\n        <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n          <div className=\"max-w-4xl mx-auto flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n              🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n            </Link>\n            <div className=\"text-purple-400 font-mono\">\n              Gaming Blog - Step 1 of 3\n            </div>\n          </div>\n        </nav>\n\n        <div className=\"max-w-4xl mx-auto p-6\">\n          {/* Phase 1: Initial 10-second countdown */}\n          {!secondPhase && initialCountdown > 0 && (\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6 text-center\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">\n                🎮 Preparing Your Gaming Content\n              </h2>\n              <div className=\"bg-purple-600 text-white rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 text-3xl font-bold\">\n                {initialCountdown}\n              </div>\n              <p className=\"text-purple-300 mb-4\">\n                ⏳ Please wait {initialCountdown} seconds while we load your content...\n              </p>\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"w-3 h-3 bg-purple-400 rounded-full animate-bounce\"></div>\n                <div className=\"w-3 h-3 bg-pink-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                <div className=\"w-3 h-3 bg-purple-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n              </div>\n            </div>\n          )}\n\n          {/* Phase 1.5: First continue button */}\n          {!secondPhase && showFirstContinue && (\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6 text-center\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">\n                ✅ Content Ready!\n              </h2>\n              <p className=\"text-gray-300 mb-6\">\n                Your gaming content is now ready. Click continue to proceed.\n              </p>\n              <button\n                onClick={handleFirstContinue}\n                className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105\"\n              >\n                Continue →\n              </button>\n            </div>\n          )}\n\n          {/* Phase 2: Second countdown + scroll instruction */}\n          {secondPhase && secondCountdown > 0 && (\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6 text-center\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">\n                📖 Loading Gaming Blog Content\n              </h2>\n              <div className=\"bg-green-600 text-white rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 text-3xl font-bold\">\n                {secondCountdown}\n              </div>\n              <p className=\"text-green-300 mb-4\">\n                ⏳ Please wait {secondCountdown} seconds while content loads...\n              </p>\n              <p className=\"text-yellow-300 text-lg font-semibold mb-4\">\n                👇 Scroll down to read the gaming blog while you wait!\n              </p>\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-bounce\"></div>\n                <div className=\"w-3 h-3 bg-yellow-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n              </div>\n            </div>\n          )}\n\n\n\n          {/* Blog Content - Only show during second phase */}\n          {secondPhase && (\n            <article className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n            <h1 className=\"text-3xl font-bold text-white mb-6\">\n              🎮 The Ultimate Guide to Gaming in 2024\n            </h1>\n            \n            <div className=\"prose prose-invert max-w-none\">\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                Gaming has evolved tremendously over the past decade, and 2024 promises to be one of the most exciting years yet for gamers worldwide. \n                From cutting-edge graphics to immersive virtual reality experiences, the gaming industry continues to push boundaries and redefine entertainment.\n              </p>\n\n              <h2 className=\"text-2xl font-semibold text-purple-300 mb-4 mt-8\">🚀 Latest Gaming Trends</h2>\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                The rise of cloud gaming has made high-quality gaming accessible to more people than ever before. Services like Xbox Game Pass, \n                PlayStation Now, and Google Stadia have revolutionized how we access and play games. No longer do you need expensive hardware \n                to enjoy the latest AAA titles.\n              </p>\n\n\n\n              <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">🎯 Best Gaming Platforms</h2>\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                Whether you're a PC master race enthusiast or a console loyalist, there's never been a better time to be a gamer. \n                The PlayStation 5 and Xbox Series X/S have brought 4K gaming and ray tracing to the mainstream, while the Nintendo Switch \n                continues to dominate the portable gaming market.\n              </p>\n\n              <ul className=\"text-gray-300 mb-6 space-y-2\">\n                <li>• <strong className=\"text-purple-300\">PC Gaming:</strong> Ultimate customization and performance</li>\n                <li>• <strong className=\"text-purple-300\">PlayStation 5:</strong> Exclusive titles and innovative DualSense controller</li>\n                <li>• <strong className=\"text-purple-300\">Xbox Series X/S:</strong> Game Pass integration and backward compatibility</li>\n                <li>• <strong className=\"text-purple-300\">Nintendo Switch:</strong> Portable gaming perfection</li>\n              </ul>\n\n              <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">🏆 Top Games to Play Right Now</h2>\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                The gaming landscape is filled with incredible titles across all genres. From epic single-player adventures to competitive \n                multiplayer experiences, there's something for every type of gamer.\n              </p>\n\n\n\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                Action-adventure games like \"The Legend of Zelda: Tears of the Kingdom\" and \"Spider-Man 2\" have set new standards \n                for open-world exploration and storytelling. Meanwhile, competitive games like \"Valorant\" and \"Apex Legends\" \n                continue to evolve with regular updates and seasonal content.\n              </p>\n\n              <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">💡 Gaming Tips for Beginners</h2>\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                Starting your gaming journey can be overwhelming with so many options available. Here are some essential tips \n                to help you get started and make the most of your gaming experience.\n              </p>\n\n              <div className=\"bg-purple-900/20 rounded-lg p-6 mb-6\">\n                <h3 className=\"text-xl font-semibold text-purple-300 mb-3\">Essential Gaming Setup</h3>\n                <ul className=\"text-gray-300 space-y-2\">\n                  <li>• Invest in a comfortable gaming chair for long sessions</li>\n                  <li>• Good headphones or speakers for immersive audio</li>\n                  <li>• Stable internet connection for online gaming</li>\n                  <li>• Proper lighting to reduce eye strain</li>\n                </ul>\n              </div>\n\n\n\n              <h2 className=\"text-2xl font-semibold text-purple-300 mb-4\">🌟 The Future of Gaming</h2>\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                As we look ahead, the future of gaming is incredibly bright. Virtual reality is becoming more accessible, \n                artificial intelligence is creating more dynamic game worlds, and cross-platform play is bringing gamers together \n                regardless of their chosen platform.\n              </p>\n\n              <p className=\"text-gray-300 mb-8 leading-relaxed\">\n                The integration of blockchain technology and NFTs in gaming is also creating new opportunities for players \n                to truly own their in-game assets. While still in its early stages, this technology could revolutionize \n                how we think about digital ownership in games.\n              </p>\n            </div>\n          </article>\n          )}\n\n          {/* Final Continue Button - Only shows after second countdown */}\n          {secondPhase && showFinalContinue && (\n            <div className=\"text-center mb-8\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-4\">\n                <h3 className=\"text-xl font-bold text-green-400 mb-4\">🎉 Ready to Continue!</h3>\n                <p className=\"text-gray-300 mb-4\">\n                  You've read the gaming content. Now proceed to the next challenge!\n                </p>\n              </div>\n              <button\n                onClick={handleFinalContinue}\n                className=\"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 text-lg\"\n              >\n                Continue to Math Quiz →\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Montag Multitag Ad Network Script */}\n        <Script\n          src=\"https://fpyf8.com/88/tag.min.js\"\n          data-zone=\"156349\"\n          async\n          data-cfasync=\"false\"\n          strategy=\"afterInteractive\"\n          onLoad={() => console.log('Page 1: Montag multitag script loaded')}\n          onError={() => console.log('Page 1: Montag multitag script failed')}\n        />\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,oCAAoC;IACnF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,8BAA8B;IAC5F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,6BAA6B;IAChG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,6BAA6B;IACzF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,6BAA6B;IAChG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,2BAA2B;IAElF,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,YAAY;YACV,aAAa;YACb,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,uCAAuC;QACvC,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;gBACzD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,EAAE;oBACf,YAAY;gBACd;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,GAAG,CAAC;YACd;QACF;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,SAAS,mBAAmB,KAAK,CAAC,aAAa;YAC9D,MAAM,QAAQ,YAAY;gBACxB,oBAAoB,CAAC;oBACnB,IAAI,QAAQ,GAAG;wBACb,cAAc;wBACd,qBAAqB;wBACrB,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAS;QAAO;QAAkB;KAAY;IAElD,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,kBAAkB,GAAG;YACtC,MAAM,QAAQ,YAAY;gBACxB,mBAAmB,CAAC;oBAClB,IAAI,QAAQ,GAAG;wBACb,cAAc;wBACd,qBAAqB;wBACrB,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAa;KAAgB;IAEjC,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,OAAO;YACtB,MAAM,WAAW,YAAY;gBAC3B,+BAA+B;gBAC/B,MAAM,aAAa,SAAS,gBAAgB,CAAC;gBAC7C,MAAM,gBAAgB,SAAS,gBAAgB,CAAC;gBAChD,MAAM,YAAY,SAAS,gBAAgB,CAAC;gBAE5C,QAAQ,GAAG,CAAC,mCAAmC;oBAC7C,YAAY,WAAW,MAAM;oBAC7B,eAAe,cAAc,MAAM;oBACnC,WAAW,UAAU,MAAM;oBAC3B,cAAc,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC7C;YACF,GAAG,OAAO,wBAAwB;YAElC,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAS;KAAM;IAEnB,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC;QACZ,eAAe;QACf,qBAAqB;IACvB;IAEA,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,uBAAuB,CAAC;IACtD;IAEA,yDAAyD;IACzD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,4DAA4D;IAE5D,qBACE;;0BACE,8OAAC,oKAAA,CAAA,UAAI;0BACH,cAAA,8OAAC;8BAAM;;;;;;;;;;;0BAET,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;wCAAmF;sDACvG,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAElG,8OAAC;oCAAI,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAM/C,8OAAC;wBAAI,WAAU;;4BAEZ,CAAC,eAAe,mBAAmB,mBAClC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAE,WAAU;;4CAAuB;4CACnB;4CAAiB;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;gDAAkD,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAC/F,8OAAC;gDAAI,WAAU;gDAAoD,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;;;;;;;4BAMtG,CAAC,eAAe,mCACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAOJ,eAAe,kBAAkB,mBAChC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAE,WAAU;;4CAAsB;4CAClB;4CAAgB;;;;;;;kDAEjC,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;gDAAoD,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DACjG,8OAAC;gDAAI,WAAU;gDAAmD,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;;;;;;;4BAQrG,6BACC,8OAAC;gCAAQ,WAAU;;kDACnB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAInD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAKlD,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAQlD,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAMlD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;0EAAE,8OAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAmB;;;;;;;kEAC7D,8OAAC;;4DAAG;0EAAE,8OAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAuB;;;;;;;kEACjE,8OAAC;;4DAAG;0EAAE,8OAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAyB;;;;;;;kEACnE,8OAAC;;4DAAG;0EAAE,8OAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAyB;;;;;;;;;;;;;0DAGrE,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAOlD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAMlD,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAKlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAMR,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAMlD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;4BAUrD,eAAe,mCACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAIpC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,8OAAC,8HAAA,CAAA,UAAM;wBACL,KAAI;wBACJ,aAAU;wBACV,KAAK;wBACL,gBAAa;wBACb,UAAS;wBACT,QAAQ,IAAM,QAAQ,GAAG,CAAC;wBAC1B,SAAS,IAAM,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;AAKrC", "debugId": null}}]}