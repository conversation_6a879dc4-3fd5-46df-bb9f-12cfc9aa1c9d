{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/s/%5BshortCode%5D/verify-challenge-x9k2m/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Script from 'next/script';\nimport Head from 'next/head';\n\ninterface Question {\n  question: string;\n  options: number[];\n  correct: number;\n}\n\nexport default function QuizPage() {\n  const params = useParams();\n  const router = useRouter();\n  const shortCode = params.shortCode as string;\n  \n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [score, setScore] = useState(0);\n  const [showResult, setShowResult] = useState(false);\n  const [wrongAnswer, setWrongAnswer] = useState(false);\n  const [countdown, setCountdown] = useState(30);\n  const [showContinue, setShowContinue] = useState(false);\n\n  // Generate random math questions\n  const generateQuestions = (): Question[] => {\n    const questions: Question[] = [];\n    \n    for (let i = 0; i < 3; i++) {\n      const type = Math.floor(Math.random() * 3); // 0: addition, 1: subtraction, 2: multiplication\n      let question: Question;\n      \n      if (type === 0) {\n        // Addition\n        const a = Math.floor(Math.random() * 50) + 1;\n        const b = Math.floor(Math.random() * 50) + 1;\n        const correct = a + b;\n        const options = [correct];\n        while (options.length < 4) {\n          const wrong = correct + Math.floor(Math.random() * 20) - 10;\n          if (wrong > 0 && !options.includes(wrong)) {\n            options.push(wrong);\n          }\n        }\n        question = {\n          question: `${a} + ${b} = ?`,\n          options: options.sort(() => Math.random() - 0.5),\n          correct\n        };\n      } else if (type === 1) {\n        // Subtraction\n        const a = Math.floor(Math.random() * 50) + 20;\n        const b = Math.floor(Math.random() * (a - 1)) + 1;\n        const correct = a - b;\n        const options = [correct];\n        while (options.length < 4) {\n          const wrong = correct + Math.floor(Math.random() * 20) - 10;\n          if (wrong >= 0 && !options.includes(wrong)) {\n            options.push(wrong);\n          }\n        }\n        question = {\n          question: `${a} - ${b} = ?`,\n          options: options.sort(() => Math.random() - 0.5),\n          correct\n        };\n      } else {\n        // Multiplication\n        const a = Math.floor(Math.random() * 12) + 1;\n        const b = Math.floor(Math.random() * 12) + 1;\n        const correct = a * b;\n        const options = [correct];\n        while (options.length < 4) {\n          const wrong = correct + Math.floor(Math.random() * 20) - 10;\n          if (wrong > 0 && !options.includes(wrong)) {\n            options.push(wrong);\n          }\n        }\n        question = {\n          question: `${a} × ${b} = ?`,\n          options: options.sort(() => Math.random() - 0.5),\n          correct\n        };\n      }\n      \n      questions.push(question);\n    }\n    \n    return questions;\n  };\n\n  const [questions] = useState<Question[]>(generateQuestions());\n\n  // Countdown for wrong answers\n  useEffect(() => {\n    if (wrongAnswer && countdown > 0) {\n      const timer = setInterval(() => {\n        setCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(timer);\n            setShowContinue(true);\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(timer);\n    }\n  }, [wrongAnswer, countdown]);\n\n  const handleAnswer = (selectedAnswer: number) => {\n    if (selectedAnswer === questions[currentQuestion].correct) {\n      setScore(score + 1);\n      if (currentQuestion + 1 < questions.length) {\n        setCurrentQuestion(currentQuestion + 1);\n      } else {\n        setShowResult(true);\n        setShowContinue(true);\n      }\n    } else {\n      setWrongAnswer(true);\n    }\n  };\n\n  const handleContinue = () => {\n    console.log('Quiz page: Setting flag and navigating to final page');\n\n    // Set flag to indicate we're coming from quiz page\n    sessionStorage.setItem(`fromQuizPage_${shortCode}`, 'true');\n\n    // Log to verify the flag is set\n    console.log('Quiz page: Flag set:', sessionStorage.getItem(`fromQuizPage_${shortCode}`));\n\n    // Navigate to final page with a small delay to ensure sessionStorage is set\n    setTimeout(() => {\n      console.log('Quiz page: Navigating to final page');\n      router.push(`/s/${shortCode}`);\n    }, 200); // Increased delay slightly for better reliability\n  };\n\n  return (\n    <>\n      <Head>\n        <title>Math Quiz - GameHub Link</title>\n      </Head>\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n        {/* Header */}\n        <nav className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4\">\n          <div className=\"max-w-4xl mx-auto flex justify-between items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer\">\n              🎮 <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">GameHub</span>\n            </Link>\n            <div className=\"text-purple-400 font-mono\">\n              Math Quiz - Step 2 of 3\n            </div>\n          </div>\n        </nav>\n\n        <div className=\"max-w-4xl mx-auto p-6\">\n\n\n          {/* Quiz Content */}\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n            <h1 className=\"text-3xl font-bold text-white mb-6 text-center\">\n              🧮 Quick Math Challenge\n            </h1>\n\n            {!wrongAnswer && !showResult && (\n              <div className=\"text-center\">\n                <div className=\"mb-6\">\n                  <span className=\"text-purple-300 text-lg\">\n                    Question {currentQuestion + 1} of {questions.length}\n                  </span>\n                </div>\n\n                <div className=\"bg-purple-900/30 rounded-lg p-8 mb-8\">\n                  <h2 className=\"text-4xl font-bold text-white mb-8\">\n                    {questions[currentQuestion].question}\n                  </h2>\n\n                  <div className=\"grid grid-cols-2 gap-4 max-w-md mx-auto\">\n                    {questions[currentQuestion].options.map((option, index) => (\n                      <button\n                        key={index}\n                        onClick={() => handleAnswer(option)}\n                        className=\"bg-white/10 hover:bg-white/20 text-white text-xl font-semibold py-4 px-6 rounded-lg transition-all transform hover:scale-105\"\n                      >\n                        {option}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {wrongAnswer && !showContinue && (\n              <div className=\"text-center\">\n                <div className=\"bg-red-900/30 rounded-lg p-8 mb-8\">\n                  <h2 className=\"text-3xl font-bold text-red-400 mb-4\">❌ Wrong Answer!</h2>\n                  <p className=\"text-gray-300 mb-6\">\n                    You gave the wrong answer. Please wait for the countdown to finish.\n                  </p>\n                  <div className=\"bg-red-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold\">\n                    {countdown}\n                  </div>\n                  <p className=\"text-red-300\">\n                    Wait {countdown} seconds...\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {showResult && (\n              <div className=\"text-center\">\n                <div className=\"bg-green-900/30 rounded-lg p-8 mb-8\">\n                  <h2 className=\"text-3xl font-bold text-green-400 mb-4\">🎉 Congratulations!</h2>\n                  <p className=\"text-gray-300 mb-6\">\n                    You answered all questions correctly! You can now proceed to get your link.\n                  </p>\n                  <div className=\"text-2xl font-bold text-green-300\">\n                    Score: {score}/{questions.length}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n\n\n          {/* Blog Content */}\n          <article className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6\">\n            <h2 className=\"text-2xl font-bold text-white mb-4\">\n              🧠 Why Math Skills Matter in Gaming\n            </h2>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              Mathematics plays a crucial role in gaming, from calculating damage per second (DPS) in RPGs to \n              understanding probability in strategy games. Many successful gamers have strong analytical skills \n              that help them optimize their gameplay and make strategic decisions.\n            </p>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              Whether you're managing resources in a city-building game or calculating the best build order \n              in a real-time strategy game, mathematical thinking gives you a competitive edge.\n            </p>\n          </article>\n\n\n\n          {/* Continue Button */}\n          {showContinue && (\n            <div className=\"text-center mb-8\">\n              <button\n                onClick={handleContinue}\n                className=\"bg-gradient-to-r from-green-600 to-emerald-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 text-lg\"\n              >\n                Continue to Final Step →\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Montag Multitag Ad Network Script */}\n        <Script\n          src=\"https://fpyf8.com/88/tag.min.js\"\n          data-zone=\"156349\"\n          async\n          data-cfasync=\"false\"\n          strategy=\"afterInteractive\"\n          onLoad={() => console.log('Page 2: Montag multitag script loaded')}\n          onError={() => console.log('Page 2: Montag multitag script failed')}\n        />\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iCAAiC;IACjC,MAAM,oBAAoB;QACxB,MAAM,YAAwB,EAAE;QAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,iDAAiD;YAC7F,IAAI;YAEJ,IAAI,SAAS,GAAG;gBACd,WAAW;gBACX,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC3C,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU;oBAAC;iBAAQ;gBACzB,MAAO,QAAQ,MAAM,GAAG,EAAG;oBACzB,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzD,IAAI,QAAQ,KAAK,CAAC,QAAQ,QAAQ,CAAC,QAAQ;wBACzC,QAAQ,IAAI,CAAC;oBACf;gBACF;gBACA,WAAW;oBACT,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC;oBAC3B,SAAS,QAAQ,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;oBAC5C;gBACF;YACF,OAAO,IAAI,SAAS,GAAG;gBACrB,cAAc;gBACd,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK;gBAChD,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU;oBAAC;iBAAQ;gBACzB,MAAO,QAAQ,MAAM,GAAG,EAAG;oBACzB,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzD,IAAI,SAAS,KAAK,CAAC,QAAQ,QAAQ,CAAC,QAAQ;wBAC1C,QAAQ,IAAI,CAAC;oBACf;gBACF;gBACA,WAAW;oBACT,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC;oBAC3B,SAAS,QAAQ,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;oBAC5C;gBACF;YACF,OAAO;gBACL,iBAAiB;gBACjB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC3C,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU;oBAAC;iBAAQ;gBACzB,MAAO,QAAQ,MAAM,GAAG,EAAG;oBACzB,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACzD,IAAI,QAAQ,KAAK,CAAC,QAAQ,QAAQ,CAAC,QAAQ;wBACzC,QAAQ,IAAI,CAAC;oBACf;gBACF;gBACA,WAAW;oBACT,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC;oBAC3B,SAAS,QAAQ,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK;oBAC5C;gBACF;YACF;YAEA,UAAU,IAAI,CAAC;QACjB;QAEA,OAAO;IACT;IAEA,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAEzC,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,YAAY,GAAG;YAChC,MAAM,QAAQ,YAAY;gBACxB,aAAa,CAAC;oBACZ,IAAI,QAAQ,GAAG;wBACb,cAAc;wBACd,gBAAgB;wBAChB,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAa;KAAU;IAE3B,MAAM,eAAe,CAAC;QACpB,IAAI,mBAAmB,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE;YACzD,SAAS,QAAQ;YACjB,IAAI,kBAAkB,IAAI,UAAU,MAAM,EAAE;gBAC1C,mBAAmB,kBAAkB;YACvC,OAAO;gBACL,cAAc;gBACd,gBAAgB;YAClB;QACF,OAAO;YACL,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ,GAAG,CAAC;QAEZ,mDAAmD;QACnD,eAAe,OAAO,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE;QAEpD,gCAAgC;QAChC,QAAQ,GAAG,CAAC,wBAAwB,eAAe,OAAO,CAAC,CAAC,aAAa,EAAE,WAAW;QAEtF,4EAA4E;QAC5E,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,CAAC,GAAG,EAAE,WAAW;QAC/B,GAAG,MAAM,kDAAkD;IAC7D;IAEA,qBACE;;0BACE,8OAAC,oKAAA,CAAA,UAAI;0BACH,cAAA,8OAAC;8BAAM;;;;;;;;;;;0BAET,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;wCAAmF;sDACvG,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAElG,8OAAC;oCAAI,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAM/C,8OAAC;wBAAI,WAAU;;0CAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;oCAI9D,CAAC,eAAe,CAAC,4BAChB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAA0B;wDAC9B,kBAAkB;wDAAE;wDAAK,UAAU,MAAM;;;;;;;;;;;;0DAIvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,SAAS,CAAC,gBAAgB,CAAC,QAAQ;;;;;;kEAGtC,8OAAC;wDAAI,WAAU;kEACZ,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/C,8OAAC;gEAEC,SAAS,IAAM,aAAa;gEAC5B,WAAU;0EAET;+DAJI;;;;;;;;;;;;;;;;;;;;;;oCAYhB,eAAe,CAAC,8BACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,8OAAC;oDAAE,WAAU;;wDAAe;wDACpB;wDAAU;;;;;;;;;;;;;;;;;;oCAMvB,4BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC;oDAAI,WAAU;;wDAAoC;wDACzC;wDAAM;wDAAE,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAU1C,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;4BASnD,8BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,8OAAC,8HAAA,CAAA,UAAM;wBACL,KAAI;wBACJ,aAAU;wBACV,KAAK;wBACL,gBAAa;wBACb,UAAS;wBACT,QAAQ,IAAM,QAAQ,GAAG,CAAC;wBAC1B,SAAS,IAAM,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;AAKrC", "debugId": null}}]}