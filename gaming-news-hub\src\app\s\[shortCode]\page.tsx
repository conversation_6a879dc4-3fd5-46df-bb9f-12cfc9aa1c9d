'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';
import Head from 'next/head';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

export default function RedirectPage() {
  const params = useParams();
  const router = useRouter();
  const shortCode = params.shortCode as string;
  
  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [countdown, setCountdown] = useState(30); // 30 seconds countdown
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [waitingPhase, setWaitingPhase] = useState(true); // 20 second wait phase
  const [adsVisible, setAdsVisible] = useState(false); // Show ads and continue button
  const [countdownStarted, setCountdownStarted] = useState(false); // 30 second countdown phase

  // State to control whether to show final page or redirect
  const [pageState, setPageState] = useState<'loading' | 'final' | 'redirect'>('loading');

  // Check URL params and determine page behavior - run only once
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const fromQuiz = urlParams.get('quiz') === 'completed';

    console.log('Final page: URL params check - fromQuiz:', fromQuiz);

    if (fromQuiz) {
      console.log('Final page: Quiz completed - staying on final page');
      setPageState('final');
      // Clean URL to remove the parameter
      window.history.replaceState({}, '', window.location.pathname);
    } else {
      console.log('Final page: No quiz completion - will redirect to blog');
      setPageState('redirect');
    }
  }, []); // Empty dependency array - run only once

  // Handle redirect when needed
  useEffect(() => {
    if (pageState === 'redirect') {
      console.log('Final page: Executing redirect to blog page');
      router.replace(`/s/${shortCode}/content-gateway-7h3p9`);
    }
  }, [pageState, router, shortCode]);

  // Fetch link data
  useEffect(() => {
    const fetchLinkData = async () => {
      try {
        console.log('Fetching link data for shortCode:', shortCode);
        const response = await fetch(`/api/redirect/${shortCode}`);
        const data = await response.json();

        console.log('API response:', response.status, data);

        if (response.ok) {
          setLinkData(data);
          console.log('Link data set successfully:', data);
        } else {
          console.log('API error:', data.error);
          setError(data.error || 'Link not found');
        }
      } catch (err) {
        console.log('Fetch error:', err);
        setError('Failed to load link data');
      } finally {
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [shortCode]);

  // Phase 1: 20-second waiting period (no timer shown)
  useEffect(() => {
    if (!loading && !error && waitingPhase) {
      console.log('Starting 20-second waiting phase...');
      const timer = setTimeout(() => {
        setWaitingPhase(false);
        setAdsVisible(true);
        console.log('20 seconds completed - showing ads and continue button');
      }, 20000); // 20 seconds

      return () => clearTimeout(timer);
    }
  }, [loading, error, waitingPhase]);

  // Phase 2: 30-second countdown timer (visible)
  useEffect(() => {
    if (!countdownStarted || countdown <= 0) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdownStarted, countdown]);

  const handleContinueClick = () => {
    console.log('Continue button clicked! Starting 30-second countdown...');
    setCountdownStarted(true);
    setCountdown(30);
  };

  const handleRedirect = async () => {
    if (linkData?.originalUrl) {
      try {
        // Increment click count
        await fetch(`/api/redirect/${shortCode}`, { method: 'POST' });
      } catch (err) {
        console.log('Failed to increment clicks, but continuing...');
      }

      // Redirect to original URL
      window.location.href = linkData.originalUrl;
    } else {
      // Fallback if no link data
      window.location.href = 'https://google.com';
    }
  };

  // Don't show loading screen if we're about to redirect to blog page
  if (loading && pageState !== 'final') {
    return null;
  }

  // Show minimal loading only if we're staying on final page
  if (loading && pageState === 'final') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-white text-xl">Welcome! Please wait...</p>
        </div>
      </div>
    );
  }

  // Don't show error screen - replace with welcome message
  if (error) {
    setError(null); // Clear error and continue with default behavior
    setLinkData({
      originalUrl: 'https://google.com',
      clicks: 0,
      createdAt: new Date().toISOString()
    });
  }

  // Only show final page content if we should show it
  if (pageState !== 'final') {
    return null; // This will trigger redirect in useEffect
  }

  return (
    <>
      <Head>
        <title>Get Your Link - GameHub</title>
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer">
            🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
          </Link>
          <div className="text-purple-400 font-mono">
            Final Step - Step 3 of 3 | {waitingPhase ? 'Preparing...' :
             adsVisible && !countdownStarted ? 'Ready' :
             countdownStarted ? `${countdown}s remaining` : 'Ready'}
          </div>
        </div>
      </nav>

      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] p-4">
        <div className="max-w-2xl w-full">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">

            {/* Phase 1: 20-second waiting (no timer shown) */}
            {waitingPhase && (
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-6">
                  Welcome! Please wait
                </h2>
                <div className="flex items-center justify-center space-x-2 mb-6">
                  <div className="w-3 h-3 bg-purple-400 rounded-full animate-bounce"></div>
                  <div className="w-3 h-3 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-3 h-3 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
                <p className="text-gray-300">
                  Setting up your secure link...
                </p>
              </div>
            )}

            {/* Phase 2: Ads visible + Continue button */}
            {adsVisible && !countdownStarted && (
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-6">
                  Almost Ready!
                </h2>
                <p className="text-gray-300 mb-6">
                  Please wait for the advertisements to load, then click Continue
                </p>

                {/* Montag ads will appear here automatically */}
                <div className="bg-white/5 rounded-lg p-6 mb-6 min-h-[200px] flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl mb-4">📺</div>
                    <p className="text-gray-400 text-sm">
                      Advertisements loading...
                    </p>
                  </div>
                </div>

                <button
                  onClick={handleContinueClick}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105"
                >
                  Continue →
                </button>
              </div>
            )}

            {/* Phase 3: 30-second countdown (visible timer) */}
            {countdownStarted && countdown > 0 && (
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-6">
                  Please Wait
                </h2>
                <div className="bg-purple-600 text-white rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 text-3xl font-bold">
                  {countdown}
                </div>
                <p className="text-purple-300 mb-4">
                  ⏳ Please wait {countdown} seconds...
                </p>
                <div className="bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold">
                  Countdown Active
                </div>
              </div>
            )}

            {/* Countdown Display */}
            <div className="text-center mb-6">
              <div className="bg-white/5 rounded-lg p-6">
                {countdownStarted ? (
                  <div className="text-center">
                    <div className="bg-purple-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                      {countdown}
                    </div>
                    <p className="text-purple-300 mb-4">
                      ⏳ Please wait {countdown} seconds...
                    </p>
                  </div>
                ) : (
                  <div className="text-center">
                    <p className="text-yellow-300 mb-4">
                      ⏳ Click an advertisement above to start the countdown
                    </p>
                  </div>
                )}

                <div className="bg-gray-700 text-gray-300 px-8 py-3 rounded-lg font-semibold">
                  {countdownStarted ? 'Countdown Active' : 'Waiting for Ad Interaction'}
                </div>
              </div>
            </div>



            {/* Final redirect section - only show after countdown */}
            {countdown <= 0 && countdownStarted && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mt-6">
                <h2 className="text-2xl font-bold text-white mb-4 text-center">Link Ready!</h2>
                <p className="text-gray-300 mb-6 text-center">Click the button below to get your link:</p>

                <button
                  onClick={handleRedirect}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full"
                >
                  Get Your Link
                </button>

                <Link href="/" className="block mt-4 text-purple-400 hover:text-purple-300 transition-colors text-center">
                  ← Back to GameHub
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* Montag Ad Network Script */}
      <Script
        src="https://fpyf8.com/88/tag.min.js"
        data-zone="156349"
        async
        data-cfasync="false"
        strategy="afterInteractive"
      />
      </div>
    </>
  );
}
