{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/lib/urlStore.ts"], "sourcesContent": ["// Hybrid URL store - uses database in production, memory locally\nexport interface UrlData {\n  originalUrl: string;\n  clicks: number;\n  createdAt: Date;\n}\n\n// In-memory store for local development\nclass MemoryUrlStore {\n  private store = new Map<string, UrlData>();\n\n  async set(shortCode: string, data: UrlData): Promise<void> {\n    console.log('MemoryStore: Setting shortCode:', shortCode, 'data:', data);\n    this.store.set(shortCode, data);\n    console.log('MemoryStore: Current store size:', this.store.size);\n  }\n\n  async get(shortCode: string): Promise<UrlData | undefined> {\n    console.log('MemoryStore: Getting shortCode:', shortCode);\n    const result = this.store.get(shortCode);\n    console.log('MemoryStore: Found:', result ? 'YES' : 'NO');\n    console.log('MemoryStore: All keys:', Array.from(this.store.keys()));\n    return result;\n  }\n\n  async has(shortCode: string): Promise<boolean> {\n    return this.store.has(shortCode);\n  }\n\n  async getAll(): Promise<Array<{ shortCode: string } & UrlData>> {\n    return Array.from(this.store.entries()).map(([shortCode, data]) => ({\n      shortCode,\n      ...data\n    }));\n  }\n\n  async incrementClicks(shortCode: string): Promise<boolean> {\n    const data = this.store.get(shortCode);\n    if (data) {\n      data.clicks += 1;\n      this.store.set(shortCode, data);\n      return true;\n    }\n    return false;\n  }\n}\n\n// Database store for production\nclass DatabaseUrlStore {\n  private initialized = false;\n\n  async init() {\n    if (!this.initialized) {\n      const { initDatabase } = await import('./database');\n      await initDatabase();\n      this.initialized = true;\n    }\n  }\n\n  async set(shortCode: string, data: UrlData): Promise<void> {\n    await this.init();\n    const { createShortUrl } = await import('./database');\n    await createShortUrl(shortCode, data.originalUrl);\n  }\n\n  async get(shortCode: string): Promise<UrlData | undefined> {\n    await this.init();\n    const { getUrlByShortCode } = await import('./database');\n    const record = await getUrlByShortCode(shortCode);\n    if (!record) return undefined;\n\n    return {\n      originalUrl: record.original_url,\n      clicks: record.clicks,\n      createdAt: record.created_at\n    };\n  }\n\n  async has(shortCode: string): Promise<boolean> {\n    await this.init();\n    const { checkShortCodeExists } = await import('./database');\n    return await checkShortCodeExists(shortCode);\n  }\n\n  async getAll(): Promise<Array<{ shortCode: string } & UrlData>> {\n    await this.init();\n    const { getRecentLinks } = await import('./database');\n    const records = await getRecentLinks();\n\n    return records.map(record => ({\n      shortCode: record.short_code,\n      originalUrl: record.original_url,\n      clicks: record.clicks,\n      createdAt: record.created_at\n    }));\n  }\n\n  async incrementClicks(shortCode: string): Promise<boolean> {\n    await this.init();\n    try {\n      const { incrementClicks } = await import('./database');\n      await incrementClicks(shortCode);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n}\n\n// Choose store based on environment\nfunction createUrlStore() {\n  // Use database if DATABASE_URL is provided, otherwise use memory\n  if (process.env.DATABASE_URL) {\n    console.log('Using database store');\n    return new DatabaseUrlStore();\n  } else {\n    console.log('Using memory store (local development)');\n    return new MemoryUrlStore();\n  }\n}\n\n// Export a singleton instance\nexport const urlStore = createUrlStore();\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;AAOjE,wCAAwC;AACxC,MAAM;IACI,QAAQ,IAAI,MAAuB;IAE3C,MAAM,IAAI,SAAiB,EAAE,IAAa,EAAiB;QACzD,QAAQ,GAAG,CAAC,mCAAmC,WAAW,SAAS;QACnE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW;QAC1B,QAAQ,GAAG,CAAC,oCAAoC,IAAI,CAAC,KAAK,CAAC,IAAI;IACjE;IAEA,MAAM,IAAI,SAAiB,EAAgC;QACzD,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9B,QAAQ,GAAG,CAAC,uBAAuB,SAAS,QAAQ;QACpD,QAAQ,GAAG,CAAC,0BAA0B,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAChE,OAAO;IACT;IAEA,MAAM,IAAI,SAAiB,EAAoB;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;IAEA,MAAM,SAA0D;QAC9D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,KAAK,GAAK,CAAC;gBAClE;gBACA,GAAG,IAAI;YACT,CAAC;IACH;IAEA,MAAM,gBAAgB,SAAiB,EAAoB;QACzD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,MAAM;YACR,KAAK,MAAM,IAAI;YACf,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW;YAC1B,OAAO;QACT;QACA,OAAO;IACT;AACF;AAEA,gCAAgC;AAChC,MAAM;IACI,cAAc,MAAM;IAE5B,MAAM,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,MAAM;YACN,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,MAAM,IAAI,SAAiB,EAAE,IAAa,EAAiB;QACzD,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,eAAe,WAAW,KAAK,WAAW;IAClD;IAEA,MAAM,IAAI,SAAiB,EAAgC;QACzD,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAC9B,MAAM,SAAS,MAAM,kBAAkB;QACvC,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAO;YACL,aAAa,OAAO,YAAY;YAChC,QAAQ,OAAO,MAAM;YACrB,WAAW,OAAO,UAAU;QAC9B;IACF;IAEA,MAAM,IAAI,SAAiB,EAAoB;QAC7C,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,oBAAoB,EAAE,GAAG;QACjC,OAAO,MAAM,qBAAqB;IACpC;IAEA,MAAM,SAA0D;QAC9D,MAAM,IAAI,CAAC,IAAI;QACf,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,UAAU,MAAM;QAEtB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B,WAAW,OAAO,UAAU;gBAC5B,aAAa,OAAO,YAAY;gBAChC,QAAQ,OAAO,MAAM;gBACrB,WAAW,OAAO,UAAU;YAC9B,CAAC;IACH;IAEA,MAAM,gBAAgB,SAAiB,EAAoB;QACzD,MAAM,IAAI,CAAC,IAAI;QACf,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,gBAAgB;YACtB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,oCAAoC;AACpC,SAAS;IACP,iEAAiE;IACjE,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI;IACb,OAAO;QACL,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI;IACb;AACF;AAGO,MAAM,WAAW", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/News%20Games/gaming-news-hub/src/app/api/redirect/%5BshortCode%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { urlStore } from '@/lib/urlStore';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ shortCode: string }> }\n) {\n  const { shortCode } = await params;\n\n  const linkData = await urlStore.get(shortCode);\n\n  if (!linkData) {\n    return NextResponse.json({ error: 'Link not found' }, { status: 404 });\n  }\n\n  return NextResponse.json({\n    originalUrl: linkData.originalUrl,\n    clicks: linkData.clicks,\n    createdAt: linkData.createdAt.toISOString()\n  });\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ shortCode: string }> }\n) {\n  const { shortCode } = await params;\n\n  const success = await urlStore.incrementClicks(shortCode);\n\n  if (!success) {\n    return NextResponse.json({ error: 'Link not found' }, { status: 404 });\n  }\n\n  const linkData = await urlStore.get(shortCode);\n\n  return NextResponse.json({\n    success: true,\n    clicks: linkData?.clicks || 0\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8C;IAEtD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM;IAE5B,MAAM,WAAW,MAAM,wHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC;IAEpC,IAAI,CAAC,UAAU;QACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,aAAa,SAAS,WAAW;QACjC,QAAQ,SAAS,MAAM;QACvB,WAAW,SAAS,SAAS,CAAC,WAAW;IAC3C;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA8C;IAEtD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM;IAE5B,MAAM,UAAU,MAAM,wHAAA,CAAA,WAAQ,CAAC,eAAe,CAAC;IAE/C,IAAI,CAAC,SAAS;QACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;IAEA,MAAM,WAAW,MAAM,wHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC;IAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,QAAQ,UAAU,UAAU;IAC9B;AACF", "debugId": null}}]}